/*  md5_sniffer.js  —— 适配所有 MessageDigest.update 重载 */
Java.perform(function () {

  /* ---------- 0️⃣ 小工具：把 Java byte[] 转成可读字符串 ---------- */
  function bytesToString(jbytes) {
    try {                 // 优先走 Java String( byte[] , charset )
      const Charset = Java.use('java.nio.charset.Charset');
      return Java.use('java.lang.String').$new(jbytes, Charset.forName('UTF-8'));
    } catch (e) {         // 失败就逐字转
      var out = "";
      for (var i = 0; i < jbytes.length; i++) {
        out += String.fromCharCode(jbytes[i]);
      }
      return out;
    }
  }

  /* ---------- 1️⃣ Hook java.security.MessageDigest ---------- */
  const MD = Java.use('java.security.MessageDigest');

  /* 1.1 处理所有 update(...) 重载 —— 把字节追加到 _frida_buf */
  MD.update.overloads.forEach(function (ol) {
    ol.implementation = function () {
      this._frida_buf = this._frida_buf || "";
      try {
        // 所有重载的第 0 个参数都是 byte[]
        const rawBytes = arguments[0];
        if (rawBytes) this._frida_buf += bytesToString(rawBytes);
      } catch (_) { /* 忽略转换失败 */ }
      return ol.apply(this, arguments);
    };
  });

  /* 1.2 处理所有 digest(...) 重载 —— 计算完后打印并清空缓存 */
  MD.digest.overloads.forEach(function (ol) {
    ol.implementation = function () {
      const outBytes = ol.apply(this, arguments);   // 真正算 MD5
      const algName  = this.getAlgorithm();        // "MD5" / "SHA-1"...
      const rawStr   = this._frida_buf || "";
      this._frida_buf = "";                        // 清空，防串页

      /* -------- 2️⃣ 过滤 & 打印 -------- */
      // 根据自己接口路径改关键字；不想过滤可直接删掉 if
      if (algName === 'MD5' && rawStr.indexOf('/feed') !== -1) {
        console.log("\n=== MD5 HIT ===");
        console.log("ALG  ->", algName);
        console.log("RAW  ->", rawStr);

        /* 把 byte[] 转十六进制 */
        const hex = Java.use('java.math.BigInteger')
                        .$new(1, outBytes).toString(16);
        console.log("MD5  ->", hex);

        /* 打印 Java 调用栈，定位混淆类/方法 */
        const Log = Java.use('android.util.Log');
        const Exc = Java.use('java.lang.Exception').$new();
        console.log(Log.getStackTraceString(Exc));
      }
      return outBytes;
    };
  });

  console.log("[*] md5_sniffer.js loaded – waiting for hits…");
});
