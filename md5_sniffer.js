// 保存为 md5_sniffer.js
Java.perform(function () {
  /* -------- 1. Hook 系统 MessageDigest -------- */
  const MD = Java.use('java.security.MessageDigest');

  // 把每次 update() 的字节累加成字符串
  MD.update.overload('[B').implementation = function (bytes) {
    this._buf = (this._buf || '') + Java.bytesToString(bytes);
    return this.update(bytes);
  };

  // 截获 digest()，在真正返回前打印
  MD.digest.overload().implementation = function () {
    const out = this.digest();   // 调用原方法得到 byte[]

    /* ------ 2. 过滤：只打印包含接口路径的哈希 ------ */
    const raw = this._buf || '';
    if (raw.indexOf('/feed/user') !== -1) {      // 关键字可换成 /multi 或 /timeline
      console.log('\n=== MD5 hit ===');
      console.log('RAW  ->', raw);               // 完整拼接串
      console.log('MD5  ->',
        Java.use('java.math.BigInteger').$new(1, out).toString(16));

      /* ---- 3. 打印 Java 调用栈，定位混淆类 ---- */
      console.log(Java.use('android.util.Log')
                      .getStackTraceString(Java.use('java.lang.Exception').$new()));
    }

    // 清空缓存，避免串到下一次
    this._buf = '';
    return out;
  };
});
