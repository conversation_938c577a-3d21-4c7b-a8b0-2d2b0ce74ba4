Java.perform(function () {
  /* 1) 如果 App 有自定义工具类，先尝试直接 Hook */
  try {
    const D = Java.use('com.finka.security.DigestUtil');   // 根据实际包名改
    D.buildDigest.implementation = function (path, query, ts) {
      const ret = this.buildDigest(path, query, ts);
      console.log('\n=== buildDigest ===');
      console.log('path   :', path);
      console.log('query  :', query);
      console.log('_t     :', ts);
      console.log('digest :', ret);
      return ret;
    };
    return;            // 找到了就直接返回
  } catch (e) { /* fallthrough */ }

  /* 2) 兜底：Hook java.security.MessageDigest */
  const MD = Java.use('java.security.MessageDigest');
  MD.update.overload('[B').implementation = function (bytes) {
    this._buf = (this._buf || '') + Java.bytesToString(bytes);
    return this.update(bytes);
  };
  MD.digest.overload().implementation = function () {
    const out = this.digest();
    if (this.getAlgorithm() === 'MD5' &&
        (this._buf || '').indexOf('/feed/user') !== -1) {
      console.log('\n=== MD5 ===');
      console.log('raw ->', this._buf);
      console.log('md5 ->', Java.use('java.math.BigInteger')
                                 .$new(1, out).toString(16));
    }
    return out;
  };
});
