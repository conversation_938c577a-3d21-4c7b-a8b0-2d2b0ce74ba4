[project]
requires-python = ">=3.8,<3.13"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "douyinliverecorder"
version = "3.0.8"
description = "An easy tool for recording live streams"
authors = ["Hmily"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/ihmily/DouyinLiveRecorder"
repository = "https://github.com/ihmily/DouyinLiveRecorder"
keywords = ["douyin", "live", "recorder"]

[tool.poetry.dependencies]
python = "^3.8"
requests = "^2.25.1"
PyExecJS = "^1.5.1"
loguru = "^0.5.3"
pycryptodome = "^3.10.1"