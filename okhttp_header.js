// okhttp_header.js
Java.perform(function () {
  const Request = Java.use('okhttp3.Request');

  Request$Builder = Java.use('okhttp3.Request$Builder');
  Request$Builder.build.implementation = function () {
    const req = this.build();
    const url = req.url().toString();
    const dg  = req.header('digest');        // 也可能叫 Digest / DIGEST

    if (url.indexOf('/feed/user') !== -1 && dg) {
      console.log('\n=== OkHttp ===');
      console.log('URL    :', url);
      console.log('digest :', dg);
    }
    return req;
  };
});
